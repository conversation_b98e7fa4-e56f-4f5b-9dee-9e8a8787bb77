# n8n Production Deployment for Azure AKS
# This file contains all necessary resources for deploying n8n

---
# Secret for n8n authentication
apiVersion: v1
kind: Secret
metadata:
  name: n8n-auth
  namespace: n8n
type: Opaque
data:
  # admin:SecurePassword123! (base64 encoded)
  username: YWRtaW4=
  password: U2VjdXJlUGFzc3dvcmQxMjMh

---
# PersistentVolumeClaim for n8n data
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: n8n-pvc
  namespace: n8n
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: managed-csi

---
# ConfigMap for n8n configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: n8n-config
  namespace: n8n
data:
  N8N_HOST: "0.0.0.0"
  N8N_PORT: "5678"
  N8N_PROTOCOL: "http"
  N8N_BASIC_AUTH_ACTIVE: "true"
  N8N_METRICS: "true"
  N8N_SECURE_COOKIE: "false"
  WEBHOOK_URL: "http://************"
  N8N_LOG_LEVEL: "info"
  N8N_LOG_OUTPUT: "console"
  EXECUTIONS_DATA_PRUNE: "true"
  EXECUTIONS_DATA_MAX_AGE: "168"

---
# n8n Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: n8n
  namespace: n8n
  labels:
    app: n8n
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: n8n
  template:
    metadata:
      labels:
        app: n8n
        version: v1
    spec:
      containers:
      - name: n8n
        image: n8nio/n8n:latest
        ports:
        - containerPort: 5678
          name: http
        env:
        - name: N8N_HOST
          valueFrom:
            configMapKeyRef:
              name: n8n-config
              key: N8N_HOST
        - name: N8N_PORT
          valueFrom:
            configMapKeyRef:
              name: n8n-config
              key: N8N_PORT
        - name: N8N_PROTOCOL
          valueFrom:
            configMapKeyRef:
              name: n8n-config
              key: N8N_PROTOCOL
        - name: N8N_BASIC_AUTH_ACTIVE
          valueFrom:
            configMapKeyRef:
              name: n8n-config
              key: N8N_BASIC_AUTH_ACTIVE
        - name: N8N_BASIC_AUTH_USER
          valueFrom:
            secretKeyRef:
              name: n8n-auth
              key: username
        - name: N8N_BASIC_AUTH_PASSWORD
          valueFrom:
            secretKeyRef:
              name: n8n-auth
              key: password
        - name: N8N_METRICS
          valueFrom:
            configMapKeyRef:
              name: n8n-config
              key: N8N_METRICS
        - name: WEBHOOK_URL
          valueFrom:
            configMapKeyRef:
              name: n8n-config
              key: WEBHOOK_URL
        - name: N8N_LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: n8n-config
              key: N8N_LOG_LEVEL
        - name: N8N_LOG_OUTPUT
          valueFrom:
            configMapKeyRef:
              name: n8n-config
              key: N8N_LOG_OUTPUT
        - name: EXECUTIONS_DATA_PRUNE
          valueFrom:
            configMapKeyRef:
              name: n8n-config
              key: EXECUTIONS_DATA_PRUNE
        - name: EXECUTIONS_DATA_MAX_AGE
          valueFrom:
            configMapKeyRef:
              name: n8n-config
              key: EXECUTIONS_DATA_MAX_AGE
        - name: N8N_SECURE_COOKIE
          valueFrom:
            configMapKeyRef:
              name: n8n-config
              key: N8N_SECURE_COOKIE
        volumeMounts:
        - name: n8n-data
          mountPath: /home/<USER>/.n8n
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 5678
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /healthz
            port: 5678
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: n8n-data
        persistentVolumeClaim:
          claimName: n8n-pvc
      securityContext:
        fsGroup: 1000
        runAsUser: 1000
        runAsGroup: 1000

---
# n8n Service
apiVersion: v1
kind: Service
metadata:
  name: n8n-service
  namespace: n8n
  labels:
    app: n8n
spec:
  selector:
    app: n8n
  ports:
  - port: 80
    targetPort: 5678
    protocol: TCP
    name: http
  type: LoadBalancer
  loadBalancerSourceRanges:
  - 0.0.0.0/0  # Restrict this to your IP ranges for security

---
# HorizontalPodAutoscaler for n8n (optional)
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: n8n-hpa
  namespace: n8n
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: n8n
  minReplicas: 1
  maxReplicas: 3
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
